const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL;

// Authentication Endpoints
export const LoginEndpoint = `${API_BASE_URL}/api/auth/login`;
export const LogoutEndpoint = `${API_BASE_URL}/api/auth/logout`;

// User Management Endpoints
export const UserEndpoints = {
  createUser: `${API_BASE_URL}/api/users`, 
  getUsers: `${API_BASE_URL}/api/users`, 
  getUserById: (id: string) => `${API_BASE_URL}/api/users/${id}`, 
  updateUser: (id: string) => `${API_BASE_URL}/api/users/${id}`, 
  deleteUser: (id: string) => `${API_BASE_URL}/api/users/${id}`, 
  changeStatus: (id: string) => `${API_BASE_URL}/api/users/${id}/status`, 
  unlockAccount: (id: string) => `${API_BASE_URL}/api/users/${id}/unlock`, 
  resetPassword: `${API_BASE_URL}/api/users/reset-password`, 
  verifyEmail: `${API_BASE_URL}/api/users/verify-email`,
};
